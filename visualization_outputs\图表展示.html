<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据可视化期末考核 - 图表展示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: bold;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .dataset-section {
            margin-bottom: 50px;
        }
        
        .dataset-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #667eea;
            color: #667eea;
        }
        
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .chart-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .chart-item:hover {
            transform: translateY(-5px);
        }
        
        .chart-item img {
            width: 100%;
            height: auto;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .chart-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .chart-description {
            font-size: 0.95em;
            color: #666;
            line-height: 1.6;
        }
        
        .summary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-top: 40px;
        }
        
        .summary h2 {
            margin-top: 0;
            font-size: 1.8em;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            text-align: center;
            background: rgba(255,255,255,0.2);
            padding: 20px;
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 1em;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 数据可视化期末考核</h1>
            <p>基于3个精选数据集的9种专业图表展示</p>
        </div>
        
        <div class="content">
            <!-- 二手房数据分析 -->
            <div class="dataset-section">
                <div class="dataset-title">🏠 二手房数据分析</div>
                <div class="chart-grid">
                    <div class="chart-item">
                        <img src="1_二手房面积总价散点图.png" alt="散点图">
                        <div class="chart-title">散点图 - 面积与总价关系</div>
                        <div class="chart-description">
                            展示了房屋面积与总价的正相关关系，不同区域的价格差异明显。朝阳区、海淀区等核心区域房价较高，体现了地理位置对房价的重要影响。
                        </div>
                    </div>
                    
                    <div class="chart-item">
                        <img src="2_各区房价分布箱形图.png" alt="箱形图">
                        <div class="chart-title">箱形图 - 各区房价分布</div>
                        <div class="chart-description">
                            通过箱形图清晰展示了各区域房价的分布特征。西城区、东城区单价最高，通州区相对较低。各区域内部价格差异较大，存在明显的价格分层现象。
                        </div>
                    </div>
                    
                    <div class="chart-item">
                        <img src="3_变量相关性热力图.png" alt="热力图">
                        <div class="chart-title">热力图 - 变量相关性分析</div>
                        <div class="chart-description">
                            相关性热力图揭示了各变量间的关系。面积与总价呈强正相关(0.8+)，房龄与单价呈负相关，户型与面积、总价均有较强正相关关系。
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 产品销售数据分析 -->
            <div class="dataset-section">
                <div class="dataset-title">📈 产品销售数据分析</div>
                <div class="chart-grid">
                    <div class="chart-item">
                        <img src="4_销售额趋势折线图.png" alt="折线图">
                        <div class="chart-title">折线图 - 销售额时间趋势</div>
                        <div class="chart-description">
                            时间序列分析显示销售额整体呈上升趋势，存在周期性波动。月末销售额通常较高，可能与促销活动相关，为制定销售策略提供了重要参考。
                        </div>
                    </div>
                    
                    <div class="chart-item">
                        <img src="5_商品销售对比柱状图.png" alt="柱状图">
                        <div class="chart-title">柱状图 - 商品销售表现对比</div>
                        <div class="chart-description">
                            双轴图表展示了不同商品的销售数量与销售额对比。A类商品销售数量最高，但B类商品单价更高，提示不同商品应采用差异化销售策略。
                        </div>
                    </div>
                    
                    <div class="chart-item">
                        <img src="6_累计销售额面积图.png" alt="面积图">
                        <div class="chart-title">面积图 - 累计销售额增长</div>
                        <div class="chart-description">
                            面积图直观展示了累计销售额的稳步增长趋势，增长速度在加快。业务发展态势良好，具有持续增长潜力，为未来规划提供了信心。
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- GDP数据分析 -->
            <div class="dataset-section">
                <div class="dataset-title">🏛️ GDP数据分析</div>
                <div class="chart-grid">
                    <div class="chart-item">
                        <img src="7_三大产业堆叠柱状图.png" alt="堆叠柱状图">
                        <div class="chart-title">堆叠柱状图 - 三大产业贡献</div>
                        <div class="chart-description">
                            堆叠柱状图清晰展示了三大产业对GDP的贡献。第三产业占比最大，是经济增长的主要动力；第二产业稳定发展；第一产业占比相对较小。
                        </div>
                    </div>
                    
                    <div class="chart-item">
                        <img src="8_经济指标对比雷达图.png" alt="雷达图">
                        <div class="chart-title">雷达图 - 经济指标对比</div>
                        <div class="chart-description">
                            雷达图多维度对比了2022年与2021年第四季度的经济指标。2022年各项指标相比2021年均有增长，经济发展保持稳定向好态势。
                        </div>
                    </div>
                    
                    <div class="chart-item">
                        <img src="9_产业结构环形图.png" alt="环形图">
                        <div class="chart-title">环形图 - 产业结构分析</div>
                        <div class="chart-description">
                            环形图展示了2022年第四季度的产业结构。第三产业占比约55%，第二产业约40%，第一产业约5%，产业结构符合发达经济体特征。
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 总结统计 -->
            <div class="summary">
                <h2>📋 项目总结</h2>
                <p>本次数据可视化项目成功完成了对3个高质量数据集的深度分析，运用9种不同类型的专业图表，全面展示了数据的多维特征和内在规律。</p>
                
                <div class="stats">
                    <div class="stat-item">
                        <span class="stat-number">3</span>
                        <span class="stat-label">精选数据集</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">9</span>
                        <span class="stat-label">专业图表</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">2909</span>
                        <span class="stat-label">最大数据量(行)</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">100%</span>
                        <span class="stat-label">任务完成度</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
