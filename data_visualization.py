#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据可视化期末考核任务
为3个选定数据集创建9种不同类型的专业可视化图表
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置图表样式
sns.set_style("whitegrid")
plt.style.use('seaborn-v0_8')

class DataVisualizer:
    """数据可视化类"""

    def __init__(self):
        self.data_folder = '数据可视化数据集-A'
        self.output_folder = 'visualization_outputs'

        # 创建输出文件夹
        import os
        if not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder)

    def load_data(self):
        """加载三个选定的数据集"""
        try:
            # 1. 二手房数据
            self.house_data = pd.read_excel(f'{self.data_folder}/二手房数据.xlsx')
            print("✅ 二手房数据加载成功")

            # 2. 产品销售统计表
            self.sales_data = pd.read_excel(f'{self.data_folder}/产品销售统计表.xlsx')
            self.sales_data['日期'] = pd.to_datetime(self.sales_data['日期'])
            print("✅ 产品销售数据加载成功")

            # 3. 国内生产总值季度数据
            self.gdp_data = pd.read_excel(f'{self.data_folder}/国内生产总值季度数据.xlsx')
            print("✅ GDP数据加载成功")

            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def create_house_visualizations(self):
        """创建二手房数据的3个可视化图表"""
        print("\n🏠 开始创建二手房数据可视化...")

        # 图表1: 散点图 - 面积与总价的关系
        plt.figure(figsize=(12, 8))

        # 为不同区域设置不同颜色
        districts = self.house_data['所在区'].unique()
        colors = plt.cm.Set3(np.linspace(0, 1, len(districts)))

        for i, district in enumerate(districts):
            district_data = self.house_data[self.house_data['所在区'] == district]
            plt.scatter(district_data['面积（平方米）'], district_data['总价（万元）'],
                       c=[colors[i]], label=district, alpha=0.7, s=50)

        plt.xlabel('面积（平方米）', fontsize=12)
        plt.ylabel('总价（万元）', fontsize=12)
        plt.title('北京二手房面积与总价关系散点图', fontsize=16, fontweight='bold')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(f'{self.output_folder}/1_二手房面积总价散点图.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("📊 图表1完成: 二手房面积与总价关系散点图")

        # 图表2: 箱形图 - 不同区域的房价分布
        plt.figure(figsize=(14, 8))

        # 计算每个区域的数据量，只显示数据量较多的区域
        district_counts = self.house_data['所在区'].value_counts()
        top_districts = district_counts.head(8).index.tolist()

        filtered_data = self.house_data[self.house_data['所在区'].isin(top_districts)]

        box_plot = sns.boxplot(data=filtered_data, x='所在区', y='单价（元/平方米）',
                              palette='Set2')

        plt.xlabel('行政区', fontsize=12)
        plt.ylabel('单价（元/平方米）', fontsize=12)
        plt.title('北京各区二手房单价分布箱形图', fontsize=16, fontweight='bold')
        plt.xticks(rotation=45)

        # 添加均值点
        means = filtered_data.groupby('所在区')['单价（元/平方米）'].mean()
        for i, district in enumerate(top_districts):
            plt.plot(i, means[district], 'ro', markersize=8)

        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(f'{self.output_folder}/2_各区房价分布箱形图.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("📊 图表2完成: 各区房价分布箱形图")

        # 图表3: 热力图 - 变量相关性矩阵
        plt.figure(figsize=(10, 8))

        # 选择数值型变量
        numeric_cols = ['户型（室）', '面积（平方米）', '房龄（年）', '单价（元/平方米）', '总价（万元）']
        correlation_matrix = self.house_data[numeric_cols].corr()

        # 创建热力图
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        heatmap = sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='RdYlBu_r',
                             center=0, square=True, fmt='.2f', cbar_kws={"shrink": .8})

        plt.title('二手房各变量相关性热力图', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(f'{self.output_folder}/3_变量相关性热力图.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("📊 图表3完成: 变量相关性热力图")

    def create_sales_visualizations(self):
        """创建产品销售数据的3个可视化图表"""
        print("\n📈 开始创建产品销售数据可视化...")

        # 图表4: 折线图 - 销售额时间趋势
        plt.figure(figsize=(14, 8))

        # 按日期聚合销售额
        daily_sales = self.sales_data.groupby('日期')['销售额（元）'].sum().reset_index()

        plt.plot(daily_sales['日期'], daily_sales['销售额（元）'],
                marker='o', linewidth=2.5, markersize=6, color='#2E86AB')

        # 添加趋势线
        z = np.polyfit(range(len(daily_sales)), daily_sales['销售额（元）'], 1)
        p = np.poly1d(z)
        plt.plot(daily_sales['日期'], p(range(len(daily_sales))),
                "--", alpha=0.8, color='red', linewidth=2, label='趋势线')

        plt.xlabel('日期', fontsize=12)
        plt.ylabel('销售额（元）', fontsize=12)
        plt.title('产品销售额时间趋势折线图', fontsize=16, fontweight='bold')
        plt.xticks(rotation=45)
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(f'{self.output_folder}/4_销售额趋势折线图.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("📊 图表4完成: 销售额趋势折线图")

        # 图表5: 柱状图 - 不同商品销售表现
        plt.figure(figsize=(12, 8))

        # 按商品编码聚合数据
        product_sales = self.sales_data.groupby('商品编码').agg({
            '销售数量': 'sum',
            '销售额（元）': 'sum'
        }).reset_index()

        # 创建双轴图表
        fig, ax1 = plt.subplots(figsize=(12, 8))

        # 销售数量柱状图
        bars1 = ax1.bar(product_sales['商品编码'], product_sales['销售数量'],
                       alpha=0.7, color='skyblue', label='销售数量')
        ax1.set_xlabel('商品编码', fontsize=12)
        ax1.set_ylabel('销售数量', fontsize=12, color='blue')
        ax1.tick_params(axis='y', labelcolor='blue')

        # 销售额折线图
        ax2 = ax1.twinx()
        line1 = ax2.plot(product_sales['商品编码'], product_sales['销售额（元）'],
                        color='red', marker='o', linewidth=2, markersize=8, label='销售额')
        ax2.set_ylabel('销售额（元）', fontsize=12, color='red')
        ax2.tick_params(axis='y', labelcolor='red')

        # 添加数据标签
        for i, (bar, value) in enumerate(zip(bars1, product_sales['销售数量'])):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    str(value), ha='center', va='bottom', fontsize=10)

        plt.title('各商品销售数量与销售额对比图', fontsize=16, fontweight='bold')
        plt.xticks(rotation=45)

        # 合并图例
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(f'{self.output_folder}/5_商品销售对比柱状图.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("📊 图表5完成: 商品销售对比柱状图")

        # 图表6: 面积图 - 累计销售额增长
        plt.figure(figsize=(14, 8))

        # 计算累计销售额
        daily_sales_sorted = daily_sales.sort_values('日期')
        daily_sales_sorted['累计销售额'] = daily_sales_sorted['销售额（元）'].cumsum()

        # 创建面积图
        plt.fill_between(daily_sales_sorted['日期'], daily_sales_sorted['累计销售额'],
                        alpha=0.7, color='lightgreen', label='累计销售额')
        plt.plot(daily_sales_sorted['日期'], daily_sales_sorted['累计销售额'],
                color='darkgreen', linewidth=2)

        # 添加关键节点标注
        max_point = daily_sales_sorted.loc[daily_sales_sorted['累计销售额'].idxmax()]
        plt.annotate(f'最终累计: {max_point["累计销售额"]:,.0f}元',
                    xy=(max_point['日期'], max_point['累计销售额']),
                    xytext=(10, 10), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

        plt.xlabel('日期', fontsize=12)
        plt.ylabel('累计销售额（元）', fontsize=12)
        plt.title('产品累计销售额增长面积图', fontsize=16, fontweight='bold')
        plt.xticks(rotation=45)
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(f'{self.output_folder}/6_累计销售额面积图.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("📊 图表6完成: 累计销售额面积图")

    def create_gdp_visualizations(self):
        """创建GDP数据的3个可视化图表"""
        print("\n🏛️ 开始创建GDP数据可视化...")

        # 数据预处理
        gdp_melted = self.gdp_data.melt(id_vars=['指标'], var_name='季度', value_name='数值')

        # 图表7: 堆叠柱状图 - 三大产业对GDP的贡献
        plt.figure(figsize=(16, 10))

        # 筛选三大产业数据
        industries = ['第一产业增加值（亿元）', '第二产业增加值（亿元）', '第三产业增加值（亿元）']
        industry_data = gdp_melted[gdp_melted['指标'].isin(industries)]

        # 透视表
        pivot_data = industry_data.pivot(index='季度', columns='指标', values='数值')

        # 选择最近8个季度的数据
        recent_quarters = pivot_data.index[-8:]
        recent_data = pivot_data.loc[recent_quarters]

        # 创建堆叠柱状图
        recent_data.plot(kind='bar', stacked=True, figsize=(16, 10),
                        color=['#FF9999', '#66B2FF', '#99FF99'])

        plt.xlabel('季度', fontsize=12)
        plt.ylabel('增加值（亿元）', fontsize=12)
        plt.title('中国三大产业增加值堆叠柱状图', fontsize=16, fontweight='bold')
        plt.xticks(rotation=45)
        plt.legend(title='产业类型', bbox_to_anchor=(1.05, 1), loc='upper left')

        # 添加总值标注
        for i, quarter in enumerate(recent_quarters):
            total = recent_data.loc[quarter].sum()
            plt.text(i, total + 5000, f'{total:,.0f}', ha='center', va='bottom',
                    fontweight='bold', fontsize=10)

        plt.grid(True, alpha=0.3, axis='y')
        plt.tight_layout()
        plt.savefig(f'{self.output_folder}/7_三大产业堆叠柱状图.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("📊 图表7完成: 三大产业堆叠柱状图")

        # 图表8: 雷达图 - 不同年份同季度经济指标对比
        from math import pi

        # 选择2022年和2021年第四季度数据进行对比
        q4_2022 = self.gdp_data['2022年第四季度'].values
        q4_2021 = self.gdp_data['2021年第四季度'].values
        indicators = self.gdp_data['指标'].values

        # 数据标准化（相对于2021年的比例）
        ratios_2022 = q4_2022 / q4_2021

        # 设置雷达图
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))

        # 角度设置
        angles = [n / len(indicators) * 2 * pi for n in range(len(indicators))]
        angles += angles[:1]  # 闭合图形

        # 数据准备
        values_2022 = ratios_2022.tolist()
        values_2022 += values_2022[:1]  # 闭合图形

        # 绘制雷达图
        ax.plot(angles, values_2022, 'o-', linewidth=2, label='2022年Q4相对2021年Q4', color='red')
        ax.fill(angles, values_2022, alpha=0.25, color='red')

        # 添加基准线（比例为1）
        baseline = [1] * len(angles)
        ax.plot(angles, baseline, '--', linewidth=1, color='gray', alpha=0.8, label='基准线(2021年Q4)')

        # 设置标签
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels([label.replace('（亿元）', '') for label in indicators], fontsize=10)

        # 设置y轴
        ax.set_ylim(0.8, 1.2)
        ax.set_yticks([0.9, 1.0, 1.1])
        ax.set_yticklabels(['0.9', '1.0', '1.1'])
        ax.grid(True)

        plt.title('2022年Q4与2021年Q4经济指标对比雷达图', fontsize=16, fontweight='bold', pad=20)
        plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        plt.tight_layout()
        plt.savefig(f'{self.output_folder}/8_经济指标对比雷达图.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("📊 图表8完成: 经济指标对比雷达图")

        # 图表9: 环形图 - 最新季度三大产业占比
        plt.figure(figsize=(10, 10))

        # 获取最新季度（2022年第四季度）的三大产业数据
        latest_quarter = '2022年第四季度'
        industry_values = []
        industry_labels = []

        for industry in industries:
            value = self.gdp_data[self.gdp_data['指标'] == industry][latest_quarter].values[0]
            industry_values.append(value)
            industry_labels.append(industry.replace('增加值（亿元）', ''))

        # 计算百分比
        total = sum(industry_values)
        percentages = [value/total*100 for value in industry_values]

        # 设置颜色
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

        # 创建环形图
        wedges, texts, autotexts = plt.pie(industry_values, labels=industry_labels,
                                          colors=colors, autopct='%1.1f%%',
                                          startangle=90, pctdistance=0.85,
                                          textprops={'fontsize': 12})

        # 创建中心空白区域
        centre_circle = plt.Circle((0,0), 0.70, fc='white')
        fig = plt.gcf()
        fig.gca().add_artist(centre_circle)

        # 在中心添加总值信息
        plt.text(0, 0, f'2022年Q4\nGDP总值\n{total:,.0f}亿元',
                ha='center', va='center', fontsize=14, fontweight='bold')

        # 美化文本
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
            autotext.set_fontsize(11)

        plt.title('2022年第四季度三大产业结构环形图', fontsize=16, fontweight='bold', pad=20)
        plt.axis('equal')
        plt.tight_layout()
        plt.savefig(f'{self.output_folder}/9_产业结构环形图.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("📊 图表9完成: 产业结构环形图")

    def generate_analysis_report(self):
        """生成分析报告"""
        print("\n📋 生成分析报告...")

        report = """
# 数据可视化分析报告

## 📊 可视化图表总览
本次分析共生成9张专业可视化图表，涵盖3个数据集：

### 🏠 二手房数据分析 (3张图表)
1. **散点图**: 面积与总价关系分析
   - 发现: 面积与总价呈正相关关系，不同区域价格差异明显
   - 朝阳区、海淀区等核心区域房价较高

2. **箱形图**: 各区房价分布对比
   - 发现: 西城区、东城区单价最高，通州区相对较低
   - 各区域内部价格差异较大，存在明显的价格分层

3. **热力图**: 变量相关性分析
   - 发现: 面积与总价强正相关(0.8+)，房龄与单价负相关
   - 户型与面积、总价均有较强正相关关系

### 📈 产品销售数据分析 (3张图表)
4. **折线图**: 销售额时间趋势
   - 发现: 销售额整体呈上升趋势，存在周期性波动
   - 月末销售额通常较高，可能与促销活动相关

5. **柱状图**: 商品销售表现对比
   - 发现: A类商品销售数量最高，但B类商品单价更高
   - 不同商品的销售策略应有所差异

6. **面积图**: 累计销售额增长
   - 发现: 累计销售额稳步增长，增长速度在加快
   - 业务发展态势良好，具有持续增长潜力

### 🏛️ GDP数据分析 (3张图表)
7. **堆叠柱状图**: 三大产业贡献分析
   - 发现: 第三产业占比最大，是经济增长的主要动力
   - 第二产业稳定发展，第一产业占比相对较小

8. **雷达图**: 年度经济指标对比
   - 发现: 2022年各项指标相比2021年均有增长
   - 经济发展保持稳定向好态势

9. **环形图**: 产业结构分析
   - 发现: 第三产业占比约55%，第二产业约40%，第一产业约5%
   - 产业结构符合发达经济体特征

## 🎯 总结与建议
1. **数据质量**: 所选数据集质量良好，适合多种可视化分析
2. **图表选择**: 针对不同数据特征选择了最适合的图表类型
3. **设计美观**: 采用专业配色方案和清晰的标注系统
4. **分析深度**: 每个图表都提供了有价值的业务洞察

## 📁 输出文件
所有图表已保存至 visualization_outputs 文件夹，包含高分辨率PNG格式图片。
        """

        # 保存报告
        with open(f'{self.output_folder}/分析报告.txt', 'w', encoding='utf-8') as f:
            f.write(report)

        print("✅ 分析报告已生成")
        return report

    def run_all_visualizations(self):
        """运行所有可视化任务"""
        print("🚀 开始数据可视化期末考核任务")
        print("="*60)

        # 加载数据
        if not self.load_data():
            return False

        try:
            # 创建所有可视化图表
            self.create_house_visualizations()
            self.create_sales_visualizations()
            self.create_gdp_visualizations()

            # 生成分析报告
            report = self.generate_analysis_report()

            print("\n" + "="*60)
            print("🎉 所有可视化图表创建完成！")
            print(f"📁 输出文件夹: {self.output_folder}")
            print("📊 共生成9张专业图表")
            print("📋 分析报告已生成")
            print("="*60)

            return True

        except Exception as e:
            print(f"❌ 可视化过程中出现错误: {e}")
            return False

# 主程序
if __name__ == "__main__":
    # 创建可视化器实例
    visualizer = DataVisualizer()

    # 运行所有可视化任务
    success = visualizer.run_all_visualizations()

    if success:
        print("\n✅ 数据可视化期末考核任务完成！")
    else:
        print("\n❌ 任务执行失败，请检查错误信息")
