
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集分析脚本
分析所有可用的数据集，为可视化任务选择最合适的数据集
"""

import pandas as pd
import os
import numpy as np

def analyze_dataset(file_path, file_name):
    """分析单个数据集"""
    try:
        print(f"\n{'='*60}")
        print(f"📊 分析数据集: {file_name}")
        print(f"{'='*60}")
        
        # 读取数据
        df = pd.read_excel(file_path)
        
        # 基本信息
        print(f"📈 数据形状: {df.shape[0]} 行 × {df.shape[1]} 列")
        print(f"📋 列名: {list(df.columns)}")
        
        # 数据类型分析
        print(f"\n🔍 数据类型分析:")
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
        datetime_cols = df.select_dtypes(include=['datetime64']).columns.tolist()
        
        print(f"   数值型列 ({len(numeric_cols)}): {numeric_cols}")
        print(f"   分类型列 ({len(categorical_cols)}): {categorical_cols}")
        print(f"   日期型列 ({len(datetime_cols)}): {datetime_cols}")
        
        # 缺失值分析
        missing_data = df.isnull().sum()
        if missing_data.sum() > 0:
            print(f"\n⚠️  缺失值:")
            for col, missing in missing_data.items():
                if missing > 0:
                    print(f"   {col}: {missing} ({missing/len(df)*100:.1f}%)")
        else:
            print(f"\n✅ 无缺失值")
        
        # 数据预览
        print(f"\n📋 数据预览 (前3行):")
        print(df.head(3).to_string())
        
        # 可视化潜力评估
        print(f"\n🎯 可视化潜力评估:")
        viz_score = 0
        viz_recommendations = []
        
        # 评估数值型数据
        if len(numeric_cols) >= 2:
            viz_score += 3
            viz_recommendations.append("适合散点图、相关性分析")
        if len(numeric_cols) >= 1:
            viz_score += 2
            viz_recommendations.append("适合直方图、箱形图、密度图")
        
        # 评估分类型数据
        if len(categorical_cols) >= 1:
            viz_score += 2
            viz_recommendations.append("适合柱状图、饼图、环形图")
        
        # 评估时间序列数据
        if len(datetime_cols) >= 1 or any('时间' in col or '日期' in col or '年' in col or '月' in col for col in df.columns):
            viz_score += 3
            viz_recommendations.append("适合折线图、时间序列分析")
        
        # 评估地理数据
        if any('地区' in col or '区域' in col or '省' in col or '市' in col or '县' in col for col in df.columns):
            viz_score += 2
            viz_recommendations.append("适合地理热力图、统计地图")
        
        # 数据量评估
        if df.shape[0] > 100:
            viz_score += 1
        if df.shape[0] > 1000:
            viz_score += 1
            
        print(f"   可视化评分: {viz_score}/10")
        print(f"   推荐图表类型: {', '.join(viz_recommendations)}")
        
        return {
            'name': file_name,
            'shape': df.shape,
            'numeric_cols': len(numeric_cols),
            'categorical_cols': len(categorical_cols),
            'datetime_cols': len(datetime_cols),
            'missing_ratio': missing_data.sum() / (df.shape[0] * df.shape[1]),
            'viz_score': viz_score,
            'recommendations': viz_recommendations,
            'data': df
        }
        
    except Exception as e:
        print(f"❌ 读取文件 {file_name} 时出错: {e}")
        return None

def main():
    """主函数"""
    print("🚀 开始分析数据集...")
    
    # 数据集文件夹
    data_folder = '数据可视化数据集-A'
    
    if not os.path.exists(data_folder):
        print(f"❌ 数据集文件夹 {data_folder} 不存在")
        return
    
    # 获取所有Excel文件
    excel_files = [f for f in os.listdir(data_folder) if f.endswith('.xlsx')]
    
    if not excel_files:
        print(f"❌ 在 {data_folder} 中未找到Excel文件")
        return
    
    print(f"📁 找到 {len(excel_files)} 个数据集文件")
    
    # 分析每个数据集
    results = []
    for file in excel_files:
        file_path = os.path.join(data_folder, file)
        result = analyze_dataset(file_path, file)
        if result:
            results.append(result)
    
    # 排序并推荐最佳数据集
    print(f"\n{'='*80}")
    print("🏆 数据集排名 (按可视化潜力排序)")
    print(f"{'='*80}")
    
    results.sort(key=lambda x: x['viz_score'], reverse=True)
    
    for i, result in enumerate(results, 1):
        print(f"{i}. {result['name']}")
        print(f"   评分: {result['viz_score']}/10")
        print(f"   数据量: {result['shape'][0]} 行 × {result['shape'][1]} 列")
        print(f"   推荐: {', '.join(result['recommendations'])}")
        print()
    
    # 推荐前3个数据集
    print(f"🎯 推荐用于可视化的前3个数据集:")
    for i in range(min(3, len(results))):
        print(f"{i+1}. {results[i]['name']} (评分: {results[i]['viz_score']}/10)")

if __name__ == "__main__":
    main()
