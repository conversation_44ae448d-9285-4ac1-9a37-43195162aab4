#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成论文中需要的代码截图
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_code_screenshot(code_text, title, filename):
    """创建代码截图"""
    fig, ax = plt.subplots(figsize=(12, 8))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    # 添加标题
    ax.text(5, 9.5, title, fontsize=16, fontweight='bold', ha='center')
    
    # 创建代码框
    code_box = FancyBboxPatch((0.5, 1), 9, 7.5, 
                             boxstyle="round,pad=0.1", 
                             facecolor='#f8f8f8', 
                             edgecolor='#cccccc',
                             linewidth=1)
    ax.add_patch(code_box)
    
    # 添加代码文本
    ax.text(1, 7.5, code_text, fontsize=10, fontfamily='monospace', 
           verticalalignment='top', horizontalalignment='left')
    
    plt.tight_layout()
    plt.savefig(f'visualization_outputs/{filename}', dpi=300, bbox_inches='tight')
    plt.close()

# 代码截图1：数据集分析
code1 = """def analyze_dataset(file_path, file_name):
    \"\"\"分析单个数据集\"\"\"
    try:
        df = pd.read_excel(file_path)
        
        # 基本信息统计
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
        datetime_cols = df.select_dtypes(include=['datetime64']).columns.tolist()
        
        # 可视化潜力评分
        viz_score = 0
        if len(numeric_cols) >= 2:
            viz_score += 3  # 适合散点图、相关性分析
        if len(categorical_cols) >= 1:
            viz_score += 2  # 适合柱状图、饼图
        if len(datetime_cols) >= 1:
            viz_score += 3  # 适合时间序列分析
            
        return viz_score
    except Exception as e:
        return 0"""

create_code_screenshot(code1, "代码截图1：数据集分析评分算法", "代码截图1_数据集分析.png")

# 代码截图2：中文字体设置
code2 = """def setup_chinese_fonts(self):
    \"\"\"设置中文字体支持\"\"\"
    chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    selected_font = None
    for font in chinese_fonts:
        if font in available_fonts:
            selected_font = font
            break
    
    if selected_font:
        plt.rcParams['font.sans-serif'] = [selected_font] + chinese_fonts
    plt.rcParams['axes.unicode_minus'] = False"""

create_code_screenshot(code2, "代码截图2：中文字体智能设置", "代码截图2_字体设置.png")

# 代码截图3：散点图实现
code3 = """# 为不同区域设置不同颜色
districts = self.house_data['所在区'].unique()
colors = plt.cm.Set3(np.linspace(0, 1, len(districts)))

for i, district in enumerate(districts):
    district_data = self.house_data[self.house_data['所在区'] == district]
    plt.scatter(district_data['面积（平方米）'], district_data['总价（万元）'], 
               c=[colors[i]], label=district, alpha=0.7, s=50)

plt.xlabel('面积（平方米）', fontsize=12)
plt.ylabel('总价（万元）', fontsize=12)
plt.title('北京二手房面积与总价关系散点图', fontsize=16, fontweight='bold')
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
plt.grid(True, alpha=0.3)"""

create_code_screenshot(code3, "代码截图3：散点图可视化实现", "代码截图3_散点图.png")

# 代码截图4：时间序列分析
code4 = """# 按日期聚合销售额
daily_sales = self.sales_data.groupby('日期')['销售额（元）'].sum().reset_index()

# 绘制主要趋势线
plt.plot(daily_sales['日期'], daily_sales['销售额（元）'], 
        marker='o', linewidth=2.5, markersize=6, color='#2E86AB')

# 添加趋势线
z = np.polyfit(range(len(daily_sales)), daily_sales['销售额（元）'], 1)
p = np.poly1d(z)
plt.plot(daily_sales['日期'], p(range(len(daily_sales))), 
        "--", alpha=0.8, color='red', linewidth=2, label='趋势线')

plt.xlabel('日期', fontsize=12)
plt.ylabel('销售额（元）', fontsize=12)
plt.title('产品销售额时间趋势折线图', fontsize=16, fontweight='bold')"""

create_code_screenshot(code4, "代码截图4：时间序列折线图实现", "代码截图4_折线图.png")

print("✅ 所有代码截图已生成完成！")
print("📁 截图文件保存在 visualization_outputs 文件夹中")
