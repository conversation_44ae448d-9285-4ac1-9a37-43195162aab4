#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中文字体显示
"""

import matplotlib.pyplot as plt
import matplotlib
import numpy as np

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

# 创建测试图表
fig, ax = plt.subplots(figsize=(10, 6))

# 测试数据
categories = ['北京', '上海', '广州', '深圳', '杭州']
values = [100, 85, 75, 90, 80]

# 创建柱状图
bars = ax.bar(categories, values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'])

# 设置标题和标签
ax.set_title('中文字体显示测试图表', fontsize=16, fontweight='bold')
ax.set_xlabel('城市', fontsize=12)
ax.set_ylabel('数值', fontsize=12)

# 添加数值标签
for bar, value in zip(bars, values):
    ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
            str(value), ha='center', va='bottom', fontsize=10)

# 添加网格
ax.grid(True, alpha=0.3)

# 保存图表
plt.tight_layout()
plt.savefig('visualization_outputs/中文字体测试.png', dpi=300, bbox_inches='tight')
plt.show()

print("✅ 中文字体测试完成！")
print("📁 测试图表已保存到: visualization_outputs/中文字体测试.png")
